package org.jeecg.modules.occu.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.occu.entity.*;
import org.jeecg.modules.occu.service.*;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 职业病问诊
 * @Author: jeecg-boot
 * @Date: 2024-05-09
 * @Version: V1.0
 */
@Api(tags = "职业病问诊")
@RestController
@RequestMapping("/occu/zyInquiry")
@Slf4j
public class ZyInquiryController extends JeecgController<ZyInquiry, IZyInquiryService> {

    @Autowired
    private IZyInquiryService zyInquiryService;

    @Autowired
    private IZyInquiryOccuHistoryService zyInquiryOccuHistoryService;

    @Autowired
    private IZyInquiryRadiationHistoryService zyInquiryRadiationHistoryService;

    @Autowired
    private IZyInquiryFamilyHistoryService zyInquiryFamilyHistoryService;

    @Autowired
    private IZyInquiryMaritalStatusService zyInquiryMaritalStatusService;

    @Autowired
    private IZyInquirySymptomService zyInquirySymptomService;

    @Autowired
    private IZyInquiryDiseaseHistoryService zyInquiryDiseaseHistoryService;

    @Autowired
    private ICustomerRegService customerRegService;


    /*---------------------------------主表处理-begin-------------------------------------*/

    @AutoLog(value = "职业病问诊-通过体检登记ID查询信息是否完整")
    @ApiOperation(value = "职业病问诊-通过体检登记ID查询信息是否完整", notes = "职业病问诊-通过体检登记ID查询信息是否完整")
    @GetMapping(value = "/canSave")
    public Result<?> canSave(@RequestParam(name = "id", required = true) String id) {
        Long occuHistoryCount = zyInquiryOccuHistoryService.count(new QueryWrapper<ZyInquiryOccuHistory>().eq("inquiry_id", id));
        return Result.OK(occuHistoryCount > 0);
    }

    /**
     * getByRegId
     *
     * @return
     */
    @AutoLog(value = "职业病问诊-通过体检登记ID查询")
    @ApiOperation(value = "职业病问诊-通过体检登记ID查询", notes = "职业病问诊-通过体检登记ID查询")
    @GetMapping(value = "/getByRegId")
    public Result<ZyInquiry> getByRegId(@RequestParam(name = "regId", required = true) String regId) {
        ZyInquiry zyInquiry = null;
        try {
            zyInquiry = zyInquiryService.getOrGenerateInquiry4Reg(regId);
            return Result.OK(zyInquiry);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @AutoLog(value = "职业病问诊-通过体检登记ID查询")
    @ApiOperation(value = "职业病问诊-通过体检登记ID查询", notes = "职业病问诊-通过体检登记ID查询")
    @GetMapping(value = "/getInquiryByRegId")
    public Result<ZyInquiry> getInquiryByRegId(@RequestParam(name = "regId", required = true) String regId) {
        ZyInquiry zyInquiry = null;
        try {
            zyInquiry = zyInquiryService.getInquiryByRegId(regId);
            return Result.OK(zyInquiry);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分页列表查询
     *
     * @param zyInquiry
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "职业病问诊-分页列表查询")
    @ApiOperation(value = "职业病问诊-分页列表查询", notes = "职业病问诊-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ZyInquiry>> queryPageList(ZyInquiry zyInquiry, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ZyInquiry> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiry, req.getParameterMap());
        Page<ZyInquiry> page = new Page<ZyInquiry>(pageNo, pageSize);
        IPage<ZyInquiry> pageList = zyInquiryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param zyInquiry
     * @return
     */
    @AutoLog(value = "职业病问诊-添加")
    @ApiOperation(value = "职业病问诊-添加", notes = "职业病问诊-添加")
    @RequiresPermissions("occu:zy_inquiry:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ZyInquiry zyInquiry) {
        zyInquiryService.save(zyInquiry);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zyInquiry
     * @return
     */
    @AutoLog(value = "职业病问诊-编辑")
    @ApiOperation(value = "职业病问诊-编辑", notes = "职业病问诊-编辑")
    @RequiresPermissions("occu:zy_inquiry:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ZyInquiry zyInquiry) {
        zyInquiryService.updateById(zyInquiry);
        return Result.OK("保存成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "职业病问诊-通过id删除")
    @ApiOperation(value = "职业病问诊-通过id删除", notes = "职业病问诊-通过id删除")
    @RequiresPermissions("occu:zy_inquiry:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        zyInquiryService.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "职业病问诊-批量删除")
    @ApiOperation(value = "职业病问诊-批量删除", notes = "职业病问诊-批量删除")
    @RequiresPermissions("occu:zy_inquiry:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.zyInquiryService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequiresPermissions("occu:zy_inquiry:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyInquiry zyInquiry) {
        return super.exportXls(request, zyInquiry, ZyInquiry.class, "职业病问诊");
    }

    /**
     * 导入
     *
     * @return
     */
    @RequiresPermissions("occu:zy_inquiry:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZyInquiry.class);
    }
    /*---------------------------------主表处理-end-------------------------------------*/


    /*--------------------------------子表处理-职业史-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    //@AutoLog(value = "职业史-通过主表ID查询")
    @ApiOperation(value = "职业史-通过主表ID查询", notes = "职业史-通过主表ID查询")
    @GetMapping(value = "/listZyInquiryOccuHistoryByMainId")
    public Result<IPage<ZyInquiryOccuHistory>> listZyInquiryOccuHistoryByMainId(ZyInquiryOccuHistory zyInquiryOccuHistory, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ZyInquiryOccuHistory> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiryOccuHistory, req.getParameterMap());
        Page<ZyInquiryOccuHistory> page = new Page<ZyInquiryOccuHistory>(pageNo, pageSize);
        IPage<ZyInquiryOccuHistory> pageList = zyInquiryOccuHistoryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param zyInquiryOccuHistory
     * @return
     */
    @AutoLog(value = "职业史-添加")
    @ApiOperation(value = "职业史-添加", notes = "职业史-添加")
    @PostMapping(value = "/addZyInquiryOccuHistory")
    public Result<String> addZyInquiryOccuHistory(@RequestBody ZyInquiryOccuHistory zyInquiryOccuHistory) {
        zyInquiryOccuHistoryService.save(zyInquiryOccuHistory);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zyInquiryOccuHistory
     * @return
     */
    @AutoLog(value = "职业史-编辑")
    @ApiOperation(value = "职业史-编辑", notes = "职业史-编辑")
    @RequestMapping(value = "/editZyInquiryOccuHistory", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> editZyInquiryOccuHistory(@RequestBody ZyInquiryOccuHistory zyInquiryOccuHistory) {
        zyInquiryOccuHistoryService.updateById(zyInquiryOccuHistory);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "职业史-通过id删除")
    @ApiOperation(value = "职业史-通过id删除", notes = "职业史-通过id删除")
    @DeleteMapping(value = "/deleteZyInquiryOccuHistory")
    public Result<String> deleteZyInquiryOccuHistory(@RequestParam(name = "id", required = true) String id) {
        zyInquiryOccuHistoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "职业史-批量删除")
    @ApiOperation(value = "职业史-批量删除", notes = "职业史-批量删除")
    @DeleteMapping(value = "/deleteBatchZyInquiryOccuHistory")
    public Result<String> deleteBatchZyInquiryOccuHistory(@RequestParam(name = "ids", required = true) String ids) {
        this.zyInquiryOccuHistoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequestMapping(value = "/exportZyInquiryOccuHistory")
    public ModelAndView exportZyInquiryOccuHistory(HttpServletRequest request, ZyInquiryOccuHistory zyInquiryOccuHistory) {
        // Step.1 组装查询条件
        QueryWrapper<ZyInquiryOccuHistory> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiryOccuHistory, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<ZyInquiryOccuHistory> pageList = zyInquiryOccuHistoryService.list(queryWrapper);
        List<ZyInquiryOccuHistory> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "职业史");
        mv.addObject(NormalExcelConstants.CLASS, ZyInquiryOccuHistory.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("职业史报表", "导出人:" + sysUser.getRealname(), "职业史"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 导入
     *
     * @return
     */
    @RequestMapping(value = "/importZyInquiryOccuHistory/{mainId}")
    public Result<?> importZyInquiryOccuHistory(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ZyInquiryOccuHistory> list = ExcelImportUtil.importExcel(file.getInputStream(), ZyInquiryOccuHistory.class, params);
                for (ZyInquiryOccuHistory temp : list) {
                    temp.setInquiryId(mainId);
                }
                long start = System.currentTimeMillis();
                zyInquiryOccuHistoryService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-职业史-end----------------------------------------------*/

    /*--------------------------------子表处理-职业问诊放射史-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    //@AutoLog(value = "职业问诊放射史-通过主表ID查询")
    @ApiOperation(value = "职业问诊放射史-通过主表ID查询", notes = "职业问诊放射史-通过主表ID查询")
    @GetMapping(value = "/listZyInquiryRadiationHistoryByMainId")
    public Result<IPage<ZyInquiryRadiationHistory>> listZyInquiryRadiationHistoryByMainId(ZyInquiryRadiationHistory zyInquiryRadiationHistory, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ZyInquiryRadiationHistory> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiryRadiationHistory, req.getParameterMap());
        Page<ZyInquiryRadiationHistory> page = new Page<ZyInquiryRadiationHistory>(pageNo, pageSize);
        IPage<ZyInquiryRadiationHistory> pageList = zyInquiryRadiationHistoryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param zyInquiryRadiationHistory
     * @return
     */
    @AutoLog(value = "职业问诊放射史-添加")
    @ApiOperation(value = "职业问诊放射史-添加", notes = "职业问诊放射史-添加")
    @PostMapping(value = "/addZyInquiryRadiationHistory")
    public Result<String> addZyInquiryRadiationHistory(@RequestBody ZyInquiryRadiationHistory zyInquiryRadiationHistory) {
        zyInquiryRadiationHistoryService.save(zyInquiryRadiationHistory);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zyInquiryRadiationHistory
     * @return
     */
    @AutoLog(value = "职业问诊放射史-编辑")
    @ApiOperation(value = "职业问诊放射史-编辑", notes = "职业问诊放射史-编辑")
    @RequestMapping(value = "/editZyInquiryRadiationHistory", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> editZyInquiryRadiationHistory(@RequestBody ZyInquiryRadiationHistory zyInquiryRadiationHistory) {
        zyInquiryRadiationHistoryService.updateById(zyInquiryRadiationHistory);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "职业问诊放射史-通过id删除")
    @ApiOperation(value = "职业问诊放射史-通过id删除", notes = "职业问诊放射史-通过id删除")
    @DeleteMapping(value = "/deleteZyInquiryRadiationHistory")
    public Result<String> deleteZyInquiryRadiationHistory(@RequestParam(name = "id", required = true) String id) {
        zyInquiryRadiationHistoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "职业问诊放射史-批量删除")
    @ApiOperation(value = "职业问诊放射史-批量删除", notes = "职业问诊放射史-批量删除")
    @DeleteMapping(value = "/deleteBatchZyInquiryRadiationHistory")
    public Result<String> deleteBatchZyInquiryRadiationHistory(@RequestParam(name = "ids", required = true) String ids) {
        this.zyInquiryRadiationHistoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequestMapping(value = "/exportZyInquiryRadiationHistory")
    public ModelAndView exportZyInquiryRadiationHistory(HttpServletRequest request, ZyInquiryRadiationHistory zyInquiryRadiationHistory) {
        // Step.1 组装查询条件
        QueryWrapper<ZyInquiryRadiationHistory> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiryRadiationHistory, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<ZyInquiryRadiationHistory> pageList = zyInquiryRadiationHistoryService.list(queryWrapper);
        List<ZyInquiryRadiationHistory> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "职业问诊放射史");
        mv.addObject(NormalExcelConstants.CLASS, ZyInquiryRadiationHistory.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("职业问诊放射史报表", "导出人:" + sysUser.getRealname(), "职业问诊放射史"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 导入
     *
     * @return
     */
    @RequestMapping(value = "/importZyInquiryRadiationHistory/{mainId}")
    public Result<?> importZyInquiryRadiationHistory(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ZyInquiryRadiationHistory> list = ExcelImportUtil.importExcel(file.getInputStream(), ZyInquiryRadiationHistory.class, params);
                for (ZyInquiryRadiationHistory temp : list) {
                    temp.setInquiryId(mainId);
                }
                long start = System.currentTimeMillis();
                zyInquiryRadiationHistoryService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-职业问诊放射史-end----------------------------------------------*/

    /*--------------------------------子表处理-职业问诊家族史-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    //@AutoLog(value = "职业问诊家族史-通过主表ID查询")
    @ApiOperation(value = "职业问诊家族史-通过主表ID查询", notes = "职业问诊家族史-通过主表ID查询")
    @GetMapping(value = "/listZyInquiryFamilyHistoryByMainId")
    public Result<IPage<ZyInquiryFamilyHistory>> listZyInquiryFamilyHistoryByMainId(ZyInquiryFamilyHistory zyInquiryFamilyHistory, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ZyInquiryFamilyHistory> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiryFamilyHistory, req.getParameterMap());
        Page<ZyInquiryFamilyHistory> page = new Page<ZyInquiryFamilyHistory>(pageNo, pageSize);
        IPage<ZyInquiryFamilyHistory> pageList = zyInquiryFamilyHistoryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param zyInquiryFamilyHistory
     * @return
     */
    @AutoLog(value = "职业问诊家族史-添加")
    @ApiOperation(value = "职业问诊家族史-添加", notes = "职业问诊家族史-添加")
    @PostMapping(value = "/addZyInquiryFamilyHistory")
    public Result<String> addZyInquiryFamilyHistory(@RequestBody ZyInquiryFamilyHistory zyInquiryFamilyHistory) {
        zyInquiryFamilyHistoryService.save(zyInquiryFamilyHistory);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zyInquiryFamilyHistory
     * @return
     */
    @AutoLog(value = "职业问诊家族史-编辑")
    @ApiOperation(value = "职业问诊家族史-编辑", notes = "职业问诊家族史-编辑")
    @RequestMapping(value = "/editZyInquiryFamilyHistory", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> editZyInquiryFamilyHistory(@RequestBody ZyInquiryFamilyHistory zyInquiryFamilyHistory) {
        zyInquiryFamilyHistoryService.updateById(zyInquiryFamilyHistory);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "职业问诊家族史-通过id删除")
    @ApiOperation(value = "职业问诊家族史-通过id删除", notes = "职业问诊家族史-通过id删除")
    @DeleteMapping(value = "/deleteZyInquiryFamilyHistory")
    public Result<String> deleteZyInquiryFamilyHistory(@RequestParam(name = "id", required = true) String id) {
        zyInquiryFamilyHistoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "职业问诊家族史-批量删除")
    @ApiOperation(value = "职业问诊家族史-批量删除", notes = "职业问诊家族史-批量删除")
    @DeleteMapping(value = "/deleteBatchZyInquiryFamilyHistory")
    public Result<String> deleteBatchZyInquiryFamilyHistory(@RequestParam(name = "ids", required = true) String ids) {
        this.zyInquiryFamilyHistoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequestMapping(value = "/exportZyInquiryFamilyHistory")
    public ModelAndView exportZyInquiryFamilyHistory(HttpServletRequest request, ZyInquiryFamilyHistory zyInquiryFamilyHistory) {
        // Step.1 组装查询条件
        QueryWrapper<ZyInquiryFamilyHistory> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiryFamilyHistory, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<ZyInquiryFamilyHistory> pageList = zyInquiryFamilyHistoryService.list(queryWrapper);
        List<ZyInquiryFamilyHistory> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "职业问诊家族史");
        mv.addObject(NormalExcelConstants.CLASS, ZyInquiryFamilyHistory.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("职业问诊家族史报表", "导出人:" + sysUser.getRealname(), "职业问诊家族史"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 导入
     *
     * @return
     */
    @RequestMapping(value = "/importZyInquiryFamilyHistory/{mainId}")
    public Result<?> importZyInquiryFamilyHistory(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ZyInquiryFamilyHistory> list = ExcelImportUtil.importExcel(file.getInputStream(), ZyInquiryFamilyHistory.class, params);
                for (ZyInquiryFamilyHistory temp : list) {
                    temp.setInquiryId(mainId);
                }
                long start = System.currentTimeMillis();
                zyInquiryFamilyHistoryService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-职业问诊家族史-end----------------------------------------------*/

    /*--------------------------------子表处理-职业问诊婚姻状况-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    //@AutoLog(value = "职业问诊婚姻状况-通过主表ID查询")
    @ApiOperation(value = "职业问诊婚姻状况-通过主表ID查询", notes = "职业问诊婚姻状况-通过主表ID查询")
    @GetMapping(value = "/listZyInquiryMaritalStatusByMainId")
    public Result<IPage<ZyInquiryMaritalStatus>> listZyInquiryMaritalStatusByMainId(ZyInquiryMaritalStatus zyInquiryMaritalStatus, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ZyInquiryMaritalStatus> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiryMaritalStatus, req.getParameterMap());
        Page<ZyInquiryMaritalStatus> page = new Page<ZyInquiryMaritalStatus>(pageNo, pageSize);
        IPage<ZyInquiryMaritalStatus> pageList = zyInquiryMaritalStatusService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param zyInquiryMaritalStatus
     * @return
     */
    @AutoLog(value = "职业问诊婚姻状况-添加")
    @ApiOperation(value = "职业问诊婚姻状况-添加", notes = "职业问诊婚姻状况-添加")
    @PostMapping(value = "/addZyInquiryMaritalStatus")
    public Result<String> addZyInquiryMaritalStatus(@RequestBody ZyInquiryMaritalStatus zyInquiryMaritalStatus) {
        zyInquiryMaritalStatusService.save(zyInquiryMaritalStatus);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zyInquiryMaritalStatus
     * @return
     */
    @AutoLog(value = "职业问诊婚姻状况-编辑")
    @ApiOperation(value = "职业问诊婚姻状况-编辑", notes = "职业问诊婚姻状况-编辑")
    @RequestMapping(value = "/editZyInquiryMaritalStatus", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> editZyInquiryMaritalStatus(@RequestBody ZyInquiryMaritalStatus zyInquiryMaritalStatus) {
        zyInquiryMaritalStatusService.updateById(zyInquiryMaritalStatus);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "职业问诊婚姻状况-通过id删除")
    @ApiOperation(value = "职业问诊婚姻状况-通过id删除", notes = "职业问诊婚姻状况-通过id删除")
    @DeleteMapping(value = "/deleteZyInquiryMaritalStatus")
    public Result<String> deleteZyInquiryMaritalStatus(@RequestParam(name = "id", required = true) String id) {
        zyInquiryMaritalStatusService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "职业问诊婚姻状况-批量删除")
    @ApiOperation(value = "职业问诊婚姻状况-批量删除", notes = "职业问诊婚姻状况-批量删除")
    @DeleteMapping(value = "/deleteBatchZyInquiryMaritalStatus")
    public Result<String> deleteBatchZyInquiryMaritalStatus(@RequestParam(name = "ids", required = true) String ids) {
        this.zyInquiryMaritalStatusService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequestMapping(value = "/exportZyInquiryMaritalStatus")
    public ModelAndView exportZyInquiryMaritalStatus(HttpServletRequest request, ZyInquiryMaritalStatus zyInquiryMaritalStatus) {
        // Step.1 组装查询条件
        QueryWrapper<ZyInquiryMaritalStatus> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiryMaritalStatus, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<ZyInquiryMaritalStatus> pageList = zyInquiryMaritalStatusService.list(queryWrapper);
        List<ZyInquiryMaritalStatus> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "职业问诊婚姻状况");
        mv.addObject(NormalExcelConstants.CLASS, ZyInquiryMaritalStatus.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("职业问诊婚姻状况报表", "导出人:" + sysUser.getRealname(), "职业问诊婚姻状况"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 导入
     *
     * @return
     */
    @RequestMapping(value = "/importZyInquiryMaritalStatus/{mainId}")
    public Result<?> importZyInquiryMaritalStatus(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ZyInquiryMaritalStatus> list = ExcelImportUtil.importExcel(file.getInputStream(), ZyInquiryMaritalStatus.class, params);
                for (ZyInquiryMaritalStatus temp : list) {
                    temp.setInquiryId(mainId);
                }
                long start = System.currentTimeMillis();
                zyInquiryMaritalStatusService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-职业问诊婚姻状况-end----------------------------------------------*/

    /*--------------------------------子表处理-职业问诊现有症状-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    //@AutoLog(value = "职业问诊现有症状-通过主表ID查询")
    @ApiOperation(value = "职业问诊现有症状-通过主表ID查询", notes = "职业问诊现有症状-通过主表ID查询")
    @GetMapping(value = "/listZyInquirySymptomByMainId")
    public Result<IPage<ZyInquirySymptom>> listZyInquirySymptomByMainId(ZyInquirySymptom zyInquirySymptom, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ZyInquirySymptom> queryWrapper = QueryGenerator.initQueryWrapper(zyInquirySymptom, req.getParameterMap());
        Page<ZyInquirySymptom> page = new Page<ZyInquirySymptom>(pageNo, pageSize);
        IPage<ZyInquirySymptom> pageList = zyInquirySymptomService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param zyInquirySymptom
     * @return
     */
    @AutoLog(value = "职业问诊现有症状-添加")
    @ApiOperation(value = "职业问诊现有症状-添加", notes = "职业问诊现有症状-添加")
    @PostMapping(value = "/addZyInquirySymptom")
    public Result<String> addZyInquirySymptom(@RequestBody ZyInquirySymptom zyInquirySymptom) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        zyInquirySymptom.setCheckDoctor(sysUser.getRealname());
        zyInquirySymptomService.fillSymptomName(zyInquirySymptom);
        zyInquirySymptomService.save(zyInquirySymptom);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zyInquirySymptom
     * @return
     */
    @AutoLog(value = "职业问诊现有症状-编辑")
    @ApiOperation(value = "职业问诊现有症状-编辑", notes = "职业问诊现有症状-编辑")
    @RequestMapping(value = "/editZyInquirySymptom", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> editZyInquirySymptom(@RequestBody ZyInquirySymptom zyInquirySymptom) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        zyInquirySymptomService.fillSymptomName(zyInquirySymptom);
        zyInquirySymptom.setCheckDoctor(sysUser.getRealname());









        zyInquirySymptomService.updateById(zyInquirySymptom);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "职业问诊现有症状-通过id删除")
    @ApiOperation(value = "职业问诊现有症状-通过id删除", notes = "职业问诊现有症状-通过id删除")
    @DeleteMapping(value = "/deleteZyInquirySymptom")
    public Result<String> deleteZyInquirySymptom(@RequestParam(name = "id", required = true) String id) {
        zyInquirySymptomService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "职业问诊现有症状-批量删除")
    @ApiOperation(value = "职业问诊现有症状-批量删除", notes = "职业问诊现有症状-批量删除")
    @DeleteMapping(value = "/deleteBatchZyInquirySymptom")
    public Result<String> deleteBatchZyInquirySymptom(@RequestParam(name = "ids", required = true) String ids) {
        this.zyInquirySymptomService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequestMapping(value = "/exportZyInquirySymptom")
    public ModelAndView exportZyInquirySymptom(HttpServletRequest request, ZyInquirySymptom zyInquirySymptom) {
        // Step.1 组装查询条件
        QueryWrapper<ZyInquirySymptom> queryWrapper = QueryGenerator.initQueryWrapper(zyInquirySymptom, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<ZyInquirySymptom> pageList = zyInquirySymptomService.list(queryWrapper);
        List<ZyInquirySymptom> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "职业问诊现有症状");
        mv.addObject(NormalExcelConstants.CLASS, ZyInquirySymptom.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("职业问诊现有症状报表", "导出人:" + sysUser.getRealname(), "职业问诊现有症状"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 导入
     *
     * @return
     */
    @RequestMapping(value = "/importZyInquirySymptom/{mainId}")
    public Result<?> importZyInquirySymptom(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ZyInquirySymptom> list = ExcelImportUtil.importExcel(file.getInputStream(), ZyInquirySymptom.class, params);
                for (ZyInquirySymptom temp : list) {
                    temp.setInquiryId(mainId);
                }
                long start = System.currentTimeMillis();
                zyInquirySymptomService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-职业问诊现有症状-end----------------------------------------------*/

    /*--------------------------------子表处理-职业问诊既往病史-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    //@AutoLog(value = "职业问诊既往病史-通过主表ID查询")
    @ApiOperation(value = "职业问诊既往病史-通过主表ID查询", notes = "职业问诊既往病史-通过主表ID查询")
    @GetMapping(value = "/listZyInquiryDiseaseHistoryByMainId")
    public Result<IPage<ZyInquiryDiseaseHistory>> listZyInquiryDiseaseHistoryByMainId(ZyInquiryDiseaseHistory zyInquiryDiseaseHistory, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ZyInquiryDiseaseHistory> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiryDiseaseHistory, req.getParameterMap());
        Page<ZyInquiryDiseaseHistory> page = new Page<ZyInquiryDiseaseHistory>(pageNo, pageSize);
        IPage<ZyInquiryDiseaseHistory> pageList = zyInquiryDiseaseHistoryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param zyInquiryDiseaseHistory
     * @return
     */
    @AutoLog(value = "职业问诊既往病史-添加")
    @ApiOperation(value = "职业问诊既往病史-添加", notes = "职业问诊既往病史-添加")
    @PostMapping(value = "/addZyInquiryDiseaseHistory")
    public Result<String> addZyInquiryDiseaseHistory(@RequestBody ZyInquiryDiseaseHistory zyInquiryDiseaseHistory) {
        zyInquiryDiseaseHistoryService.save(zyInquiryDiseaseHistory);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zyInquiryDiseaseHistory
     * @return
     */
    @AutoLog(value = "职业问诊既往病史-编辑")
    @ApiOperation(value = "职业问诊既往病史-编辑", notes = "职业问诊既往病史-编辑")
    @RequestMapping(value = "/editZyInquiryDiseaseHistory", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> editZyInquiryDiseaseHistory(@RequestBody ZyInquiryDiseaseHistory zyInquiryDiseaseHistory) {
        zyInquiryDiseaseHistoryService.updateById(zyInquiryDiseaseHistory);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "职业问诊既往病史-通过id删除")
    @ApiOperation(value = "职业问诊既往病史-通过id删除", notes = "职业问诊既往病史-通过id删除")
    @DeleteMapping(value = "/deleteZyInquiryDiseaseHistory")
    public Result<String> deleteZyInquiryDiseaseHistory(@RequestParam(name = "id", required = true) String id) {
        zyInquiryDiseaseHistoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "职业问诊既往病史-批量删除")
    @ApiOperation(value = "职业问诊既往病史-批量删除", notes = "职业问诊既往病史-批量删除")
    @DeleteMapping(value = "/deleteBatchZyInquiryDiseaseHistory")
    public Result<String> deleteBatchZyInquiryDiseaseHistory(@RequestParam(name = "ids", required = true) String ids) {
        this.zyInquiryDiseaseHistoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequestMapping(value = "/exportZyInquiryDiseaseHistory")
    public ModelAndView exportZyInquiryDiseaseHistory(HttpServletRequest request, ZyInquiryDiseaseHistory zyInquiryDiseaseHistory) {
        // Step.1 组装查询条件
        QueryWrapper<ZyInquiryDiseaseHistory> queryWrapper = QueryGenerator.initQueryWrapper(zyInquiryDiseaseHistory, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<ZyInquiryDiseaseHistory> pageList = zyInquiryDiseaseHistoryService.list(queryWrapper);
        List<ZyInquiryDiseaseHistory> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "职业问诊既往病史");
        mv.addObject(NormalExcelConstants.CLASS, ZyInquiryDiseaseHistory.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("职业问诊既往病史报表", "导出人:" + sysUser.getRealname(), "职业问诊既往病史"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 导入
     *
     * @return
     */
    @RequestMapping(value = "/importZyInquiryDiseaseHistory/{mainId}")
    public Result<?> importZyInquiryDiseaseHistory(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ZyInquiryDiseaseHistory> list = ExcelImportUtil.importExcel(file.getInputStream(), ZyInquiryDiseaseHistory.class, params);
                for (ZyInquiryDiseaseHistory temp : list) {
                    temp.setInquiryId(mainId);
                }
                long start = System.currentTimeMillis();
                zyInquiryDiseaseHistoryService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-职业问诊既往病史-end----------------------------------------------*/

    /*--------------------------------历史问卷记录接口-start----------------------------------------------*/

    /**
     * 根据身份证号获取历史问卷记录列表
     * @param idCard 身份证号
     * @return 历史问卷记录列表
     */
    @AutoLog(value = "职业病问诊-根据身份证号获取历史记录")
    @ApiOperation(value = "职业病问诊-根据身份证号获取历史记录", notes = "职业病问诊-根据身份证号获取历史记录")
    @GetMapping(value = "/getHistoryByIdCard")
    public Result<List<ZyInquiry>> getHistoryByIdCard(@RequestParam(name="idCard",required=true) String idCard) {
        List<ZyInquiry> historyList = zyInquiryService.getHistoryByIdCard(idCard);
        return Result.OK(historyList);
    }

    /**
     * 根据身份证号获取完整的历史问卷记录（包含子问卷数据）
     * @param idCard 身份证号
     * @return 完整的历史问卷记录列表
     */
    @AutoLog(value = "职业病问诊-根据身份证号获取完整历史记录")
    @ApiOperation(value = "职业病问诊-根据身份证号获取完整历史记录", notes = "职业病问诊-根据身份证号获取完整历史记录")
    @GetMapping(value = "/getCompleteHistoryByIdCard")
    public Result<List<Map<String, Object>>> getCompleteHistoryByIdCard(@RequestParam(name="idCard",required=true) String idCard) {
        List<Map<String, Object>> historyList = zyInquiryService.getCompleteHistoryByIdCard(idCard);
        return Result.OK(historyList);
    }

    /**
     * 根据问卷ID获取完整的问卷数据（包含所有子问卷）
     * @param inquiryId 问卷ID
     * @return 完整的问卷数据
     */
    @AutoLog(value = "职业病问诊-根据问卷ID获取完整数据")
    @ApiOperation(value = "职业病问诊-根据问卷ID获取完整数据", notes = "职业病问诊-根据问卷ID获取完整数据")
    @GetMapping(value = "/getCompleteDataById")
    public Result<Map<String, Object>> getCompleteDataById(@RequestParam(name="inquiryId",required=true) String inquiryId) {
        Map<String, Object> completeData = zyInquiryService.getCompleteDataById(inquiryId);

        return Result.OK(completeData);
    }

    /**
     * 根据体检登记ID获取完整的问卷数据（集成接口）
     * @param regId 体检登记ID
     * @return 完整的问卷数据
     */
    @AutoLog(value = "职业病问诊-根据登记ID获取完整数据")
    @ApiOperation(value = "职业病问诊-根据登记ID获取完整数据", notes = "职业病问诊-根据登记ID获取完整数据")
    @GetMapping(value = "/getCompleteDataByRegId")
    public Result<Map<String, Object>> getCompleteDataByRegId(@RequestParam(name="regId",required=true) String regId) {
        ZyInquiry inquiry = zyInquiryService.getByRegId(regId);
        if (inquiry == null) {
            return Result.OK(new HashMap<>());
        }
        Map<String, Object> completeData = zyInquiryService.getCompleteDataById(inquiry.getId());
        return Result.OK(completeData);
    }


    /**
     * 根据身份证号获取智能填写推荐数据
     * @param idCard 身份证号
     * @return 智能推荐数据列表
     */
    @AutoLog(value = "职业病问诊-智能填写推荐")
    @ApiOperation(value = "职业病问诊-智能填写推荐", notes = "职业病问诊-智能填写推荐")
    @GetMapping(value = "/getSmartFillRecommendations")
    public Result<List<Map<String, Object>>> getSmartFillRecommendations(@RequestParam(name="idCard",required=true) String idCard) {
        List<Map<String, Object>> recommendations = zyInquiryService.getSmartFillRecommendations(idCard);
        return Result.OK(recommendations);
    }

    /**
     * 应用智能推荐数据到当前问卷
     * @param targetRegId 目标客户登记ID
     * @param sourceInquiryId 源问卷ID
     * @param applySubQuestionnaires 是否应用子问卷数据
     * @return 操作结果
     */
    @AutoLog(value = "职业病问诊-应用智能推荐数据")
    @ApiOperation(value = "职业病问诊-应用智能推荐数据", notes = "职业病问诊-应用智能推荐数据")
    @PostMapping(value = "/applySmartFillData")
    public Result<Map<String, Object>> applySmartFillData(
            @RequestParam(name="targetRegId",required=true) String targetRegId,
            @RequestParam(name="sourceInquiryId",required=true) String sourceInquiryId,
            @RequestParam(name="applySubQuestionnaires",defaultValue="true") boolean applySubQuestionnaires) {
        Map<String, Object> result = zyInquiryService.applySmartFillData(targetRegId, sourceInquiryId, applySubQuestionnaires);
        return Result.OK(result);
    }

    /**
     * 从历史问卷拷贝数据到当前问卷
     * @param targetRegId 目标客户登记ID
     * @param sourceInquiryId 源问卷ID
     * @return 操作结果
     */
    @AutoLog(value = "职业病问诊-复制历史问卷数据")
    @ApiOperation(value = "职业病问诊-复制历史问卷数据", notes = "职业病问诊-复制历史问卷数据")
    @PostMapping(value = "/copyFromHistoryInquiry")
    public Result<Map<String, Object>> copyFromHistoryInquiry(
            @RequestParam(name="targetRegId",required=true) String targetRegId,
            @RequestParam(name="sourceInquiryId",required=true) String sourceInquiryId) {
        Map<String, Object> result = zyInquiryService.copyFromHistoryInquiry(targetRegId, sourceInquiryId);
        return Result.OK(result);
    }

    /*--------------------------------历史问卷记录接口-end----------------------------------------------*/

}
