package org.jeecg.modules.basicinfo.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.basicinfo.cache.AutoCompleteCacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 自动补全缓存管理
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Api(tags = "自动补全缓存管理")
@RestController
@RequestMapping("/basicinfo/autoCompleteCache")
@Slf4j
public class AutoCompleteCacheController {

    @Autowired
    private AutoCompleteCacheManager cacheManager;

    /**
     * 获取缓存统计信息
     */
    @AutoLog(value = "自动补全缓存-获取统计信息")
    @ApiOperation(value = "获取缓存统计信息", notes = "获取缓存统计信息")
    @GetMapping(value = "/stats")
    public Result<Object> getCacheStats() {
        try {
            Object stats = cacheManager.getCacheStats();
            return Result.OK(stats);
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 清除指定分类的缓存
     */
    @AutoLog(value = "自动补全缓存-清除分类缓存")
    @ApiOperation(value = "清除分类缓存", notes = "清除指定分类的所有缓存")
    @PostMapping(value = "/clearCategory")
    public Result<String> clearCategoryCache(@RequestParam String category) {
        try {
            boolean success = cacheManager.clearCategoryCache(category);
            if (success) {
                return Result.OK("分类缓存清除成功");
            } else {
                return Result.error("分类缓存清除失败");
            }
        } catch (Exception e) {
            log.error("清除分类缓存失败", e);
            return Result.error("清除失败: " + e.getMessage());
        }
    }

    /**
     * 清除所有自动补全缓存
     */
    @AutoLog(value = "自动补全缓存-清除所有缓存")
    @ApiOperation(value = "清除所有缓存", notes = "清除所有自动补全相关的缓存")
    @PostMapping(value = "/clearAll")
    public Result<String> clearAllCache() {
        try {
            boolean success = cacheManager.clearAllCache();
            if (success) {
                return Result.OK("所有缓存清除成功");
            } else {
                return Result.error("缓存清除失败");
            }
        } catch (Exception e) {
            log.error("清除所有缓存失败", e);
            return Result.error("清除失败: " + e.getMessage());
        }
    }

    /**
     * 检查缓存是否存在
     */
    @AutoLog(value = "自动补全缓存-检查缓存存在")
    @ApiOperation(value = "检查缓存是否存在", notes = "检查指定缓存键是否存在")
    @GetMapping(value = "/exists")
    public Result<Boolean> checkCacheExists(@RequestParam String cacheKey) {
        try {
            boolean exists = cacheManager.hasCache(cacheKey);
            return Result.OK(exists);
        } catch (Exception e) {
            log.error("检查缓存存在失败", e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 预热指定分类的缓存
     */
    @AutoLog(value = "自动补全缓存-预热分类缓存")
    @ApiOperation(value = "预热分类缓存", notes = "预热指定分类的热门项目缓存")
    @PostMapping(value = "/warmup")
    public Result<String> warmupCategoryCache(@RequestParam String category,
                                             @RequestParam(defaultValue = "10") Integer limit) {
        try {
            // 这里可以调用Service的方法来预热缓存
            // 例如：autoCompleteItemService.getPopularItems(category, limit);
            return Result.OK("缓存预热成功");
        } catch (Exception e) {
            log.error("缓存预热失败", e);
            return Result.error("预热失败: " + e.getMessage());
        }
    }
}
