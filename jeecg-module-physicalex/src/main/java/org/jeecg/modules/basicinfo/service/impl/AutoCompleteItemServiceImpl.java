package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.AutoCompleteConfig;
import org.jeecg.modules.basicinfo.entity.AutoCompleteItem;
import org.jeecg.modules.basicinfo.mapper.AutoCompleteItemMapper;
import org.jeecg.modules.basicinfo.service.IAutoCompleteConfigService;
import org.jeecg.modules.basicinfo.service.IAutoCompleteItemService;
import org.jeecg.modules.basicinfo.vo.AutoCompleteCreateVO;
import org.jeecg.modules.basicinfo.vo.AutoCompleteResultVO;
import org.jeecg.modules.basicinfo.vo.AutoCompleteSearchVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.jeecg.modules.basicinfo.cache.AutoCompleteCacheManager;
import org.jeecg.modules.basicinfo.util.PinyinUtil;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 通用自动补全项目服务实现
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Service
@Slf4j
public class AutoCompleteItemServiceImpl extends ServiceImpl<AutoCompleteItemMapper, AutoCompleteItem> implements IAutoCompleteItemService {

    @Autowired
    private IAutoCompleteConfigService autoCompleteConfigService;

    @Autowired
    private AutoCompleteCacheManager cacheManager;

    @Override
    public List<AutoCompleteResultVO> autoCompleteSearch(AutoCompleteSearchVO searchVO) {
        // 参数验证
        if (StringUtils.isBlank(searchVO.getCategory())) {
            throw new IllegalArgumentException("分类标识不能为空");
        }

        // 获取配置
        AutoCompleteConfig config = autoCompleteConfigService.getConfigByCategory(searchVO.getCategory());
        if (config == null) {
            config = autoCompleteConfigService.getDefaultConfig();
        }

        // 设置默认值
        if (searchVO.getLimit() == null || searchVO.getLimit() <= 0) {
            searchVO.setLimit(config.getMaxSuggestions());
        }
        if (StringUtils.isBlank(searchVO.getSearchType())) {
            searchVO.setSearchType(config.getSearchType());
        }

        List<AutoCompleteResultVO> results = new ArrayList<>();

        // 如果有关键词，进行搜索
        if (StringUtils.isNotBlank(searchVO.getKeyword())) {
            results = searchWithCache(searchVO, config);
        } else if (searchVO.getIncludePopular()) {
            // 没有关键词时返回热门推荐
            results = getPopularItems(searchVO.getCategory(), searchVO.getLimit());
        }

        // 如果允许自动创建且没有完全匹配的结果，添加"新建"选项
        if (config.getAutoCreateEnabled() == 1 && StringUtils.isNotBlank(searchVO.getKeyword())) {
            boolean hasExactMatch = results.stream()
                    .anyMatch(item -> searchVO.getKeyword().equalsIgnoreCase(item.getName()));
            
            if (!hasExactMatch) {
                AutoCompleteResultVO newItem = new AutoCompleteResultVO();
                newItem.setCategory(searchVO.getCategory());
                newItem.setName(searchVO.getKeyword().trim());
                newItem.setIsNew(true);
                newItem.setMatchType("new");
                newItem.setMatchScore(1);
                results.add(0, newItem); // 添加到第一位
            }
        }

        return results.stream()
                .limit(searchVO.getLimit())
                .collect(Collectors.toList());
    }

    @Override
    public List<AutoCompleteResultVO> getPopularItems(String category, Integer limit) {
        if (StringUtils.isBlank(category)) {
            return new ArrayList<>();
        }

        // 获取配置
        AutoCompleteConfig config = autoCompleteConfigService.getConfigByCategory(category);
        if (config == null) {
            config = autoCompleteConfigService.getDefaultConfig();
        }

        if (limit == null || limit <= 0) {
            limit = config.getMaxSuggestions();
        }

        // 尝试从缓存获取
        if (config.getCacheEnabled() == 1) {
            List<AutoCompleteResultVO> cachedResult = cacheManager.getPopularCache(category, limit);
            if (cachedResult != null) {
                return cachedResult;
            }
        }

        // 从数据库查询
        List<AutoCompleteResultVO> results = baseMapper.getPopularItems(category, limit);

        // 存入缓存
        if (config.getCacheEnabled() == 1 && !results.isEmpty()) {
            cacheManager.setPopularCache(category, limit, results, config.getCacheExpireHours());
        }

        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AutoCompleteItem autoCreateItem(AutoCompleteCreateVO createVO) {
        // 参数验证
        if (StringUtils.isBlank(createVO.getCategory()) || StringUtils.isBlank(createVO.getName())) {
            throw new IllegalArgumentException("分类标识和名称不能为空");
        }

        // 检查是否已存在
        String existingId = baseMapper.checkItemExists(createVO.getCategory(), createVO.getName());
        if (StringUtils.isNotBlank(existingId)) {
            return getById(existingId);
        }

        // 创建新项目
        AutoCompleteItem item = new AutoCompleteItem();
        BeanUtils.copyProperties(createVO, item);
        
        // 设置默认值
        if (item.getUseCount() == null) {
            item.setUseCount(0);
        }
        if (item.getSortOrder() == null) {
            item.setSortOrder(0);
        }
        item.setStatus(1);
        item.setDelFlag(0);

        // 如果没有提供助记码，尝试生成
        if (StringUtils.isBlank(item.getHelpChar())) {
            item.setHelpChar(PinyinUtil.generateSmartHelpChar(item.getName()));
        }

        // 如果没有提供拼音，尝试生成
        if (StringUtils.isBlank(item.getPinyin())) {
            item.setPinyin(PinyinUtil.generateSmartPinyin(item.getName()));
        }

        save(item);

        // 清除相关缓存
        cacheManager.clearCategoryCache(createVO.getCategory());

        return item;
    }

    @Override
    public boolean updateUseCount(String id, String userId, String userName) {
        if (StringUtils.isBlank(id)) {
            return false;
        }

        try {
            int updated = baseMapper.updateUseCount(id, 1);
            if (updated > 0) {
                // 异步更新统计表（这里简化处理，实际可以使用消息队列）
                // TODO: 调用存储过程更新统计表
                return true;
            }
        } catch (Exception e) {
            log.error("更新使用频次失败: id={}, error={}", id, e.getMessage());
        }
        return false;
    }

    @Override
    public boolean batchUpdateUseCount(List<String> ids, String userId, String userName) {
        if (oConvertUtils.isEmpty(ids)) {
            return false;
        }

        try {
            int updated = baseMapper.batchUpdateUseCount(ids, 1);
            return updated > 0;
        } catch (Exception e) {
            log.error("批量更新使用频次失败: ids={}, error={}", ids, e.getMessage());
        }
        return false;
    }

    @Override
    public String checkItemExists(String category, String name) {
        if (StringUtils.isBlank(category) || StringUtils.isBlank(name)) {
            return null;
        }
        return baseMapper.checkItemExists(category, name);
    }

    @Override
    public Object getCategoryStats(String category) {
        if (StringUtils.isBlank(category)) {
            return null;
        }

        Map<String, Object> stats = new HashMap<>();
        stats.put("category", category);
        stats.put("totalCount", baseMapper.getItemCountByCategory(category));
        stats.put("topUsedItems", baseMapper.getTopUsedItems(category, 5));
        
        return stats;
    }

    @Override
    public boolean clearCategoryCache(String category) {
        return cacheManager.clearCategoryCache(category);
    }

    @Override
    public boolean refreshCategoryCache(String category) {
        // 先清除缓存
        cacheManager.clearCategoryCache(category);

        // 预热热门项目缓存
        try {
            getPopularItems(category, 10);
            return true;
        } catch (Exception e) {
            log.error("刷新分类缓存失败: category={}, error={}", category, e.getMessage());
            return false;
        }
    }

    /**
     * 带缓存的搜索
     */
    private List<AutoCompleteResultVO> searchWithCache(AutoCompleteSearchVO searchVO, AutoCompleteConfig config) {
        // 如果启用缓存
        if (config.getCacheEnabled() == 1) {
            List<AutoCompleteResultVO> cachedResult = cacheManager.getSearchCache(
                    searchVO.getCategory(),
                    searchVO.getKeyword(),
                    searchVO.getSearchType(),
                    searchVO.getLimit()
            );
            if (cachedResult != null) {
                return cachedResult;
            }
        }

        // 从数据库搜索
        List<AutoCompleteResultVO> results = baseMapper.searchAutoCompleteItems(
                searchVO.getCategory(),
                searchVO.getKeyword(),
                searchVO.getSearchType(),
                searchVO.getLimit()
        );

        // 存入缓存（搜索结果缓存时间较短）
        if (config.getCacheEnabled() == 1 && !results.isEmpty()) {
            // 搜索结果缓存时间设置为配置时间的1/4
            int cacheMinutes = Math.max(config.getCacheExpireHours() * 15, 15);
            cacheManager.setSearchCache(
                    searchVO.getCategory(),
                    searchVO.getKeyword(),
                    searchVO.getSearchType(),
                    searchVO.getLimit(),
                    results,
                    cacheMinutes
            );
        }

        return results;
    }


}
