import { defHttp } from "/@/utils/http/axios";
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/occu/zyIndustry/rootList',
  save='/occu/zyIndustry/add',
  edit='/occu/zyIndustry/edit',
  deleteZyIndustry = '/occu/zyIndustry/delete',
  importExcel = '/occu/zyIndustry/importExcel',
  exportXls = '/occu/zyIndustry/exportXls',
  loadTreeData = '/occu/zyIndustry/loadTreeRoot',
  getChildList = '/occu/zyIndustry/childList',
  getChildListBatch = '/occu/zyIndustry/getChildListBatch',
  getLeafNodes = '/occu/zyIndustry/leafNodes',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 * @param params
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteZyIndustry = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.deleteZyIndustry, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDeleteZyIndustry = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteZyIndustry, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdateDict = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params },{ isTransformResponse:false });
}

/**
 * 查询全部树形节点数据
 * @param params
 */
export const loadTreeData = (params) => defHttp.get({ url: Api.loadTreeData,params });

/**
 * 查询子节点数据
 * @param params
 */
export const getChildList = (params) => defHttp.get({ url: Api.getChildList, params });
  
/**
 * 批量查询子节点数据
 * @param params
 */
export const getChildListBatch = (params) => defHttp.get({ url: Api.getChildListBatch, params },{ isTransformResponse:false });

/**
 * 获取所有叶子节点数据（用于下拉选择）
 * @param params 搜索参数 {keyword: string}
 */
export const getLeafNodes = (params) => defHttp.get({ url: Api.getLeafNodes, params });
